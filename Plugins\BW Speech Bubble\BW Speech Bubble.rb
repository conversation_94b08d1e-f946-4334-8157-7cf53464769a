#-------------------------------------------------------------------------------
# BW Speech Bubbles for v21
# Updated by NoNonever
#-------------------------------------------------------------------------------
# To use, call pbCallBub(type, eventID)
#
# Where type is either 1 or 2:
# 1 - floating bubble
# 2 - speech bubble with arrow
#-------------------------------------------------------------------------------

#-------------------------------------------------------------------------------
# Class modifiers
#-------------------------------------------------------------------------------

class Game_Temp
  attr_accessor :speechbubble_bubble
  attr_accessor :speechbubble_vp
  attr_accessor :speechbubble_arrow
  attr_accessor :speechbubble_outofrange
  attr_accessor :speechbubble_talking
  attr_accessor :speechbubble_animating
  attr_accessor :speechbubble_animation_frame
  attr_accessor :speechbubble_slide_offset
  attr_accessor :speechbubble_original_y
end

module MessageConfig
  BUBBLETEXTBASE  = Color.new(248,248,248)
  BUBBLETEXTSHADOW= Color.new(72,80,88)
end

#-------------------------------------------------------------------------------
# Function modifiers
#-------------------------------------------------------------------------------

class Window_AdvancedTextPokemon
  def text=(value)
    if value != nil && value != "" && $game_temp.speechbubble_bubble && $game_temp.speechbubble_bubble > 0
      if $game_temp.speechbubble_bubble == 1
        $game_temp.speechbubble_bubble = 0
        resizeToFit2(value,400,100)
        # Use player if speaking, otherwise the event
        if $game_temp.speechbubble_talking == -1
          speaker_x = $game_player.screen_x
          speaker_y = $game_player.screen_y
        else
          speaker = $game_map.events[$game_temp.speechbubble_talking]
          speaker_x = speaker.screen_x
          speaker_y = speaker.screen_y
        end
        # Center bubble horizontally on the event
        @x = speaker_x - (@width / 2)

        # Position bubble above or below event based on screen position
        if speaker_y < Graphics.height / 2
          # Event in upper half - bubble below
          @y = speaker_y + 64
        else
          # Event in lower half - bubble above
          @y = speaker_y - @height - 64
        end

        # Keep bubble on screen
        if @y > (Graphics.height - @height - 2)
          @y = (Graphics.height - @height - 2)
        elsif @y < 2
          @y = 2
        end
        if @x > (Graphics.width - @width - 2)
          @x = (Graphics.width - @width - 2)
        elsif @x < 2
          @x = 2
        end
      else
        $game_temp.speechbubble_bubble = 0
      end
    end
    setText(value)
  end

  # Override update method to handle speech bubble animation
  alias_method :original_update, :update
  def update
    original_update
    # Update speech bubble animation if this is a speech bubble window
    if $game_temp.speechbubble_bubble && $game_temp.speechbubble_bubble > 0
      pbUpdateSpeechBubbleAnimation(self)
    end
  end
end

def pbRepositionMessageWindow(msgwindow, linecount=2)
  msgwindow.height=32*linecount+msgwindow.borderY
  msgwindow.y=(Graphics.height)-(msgwindow.height)
  if $game_temp && $game_temp.in_battle && !$scene.respond_to?("update_basic")
    msgwindow.y=0
  elsif $game_system && $game_system.respond_to?("message_position")
    case $game_system.message_position
    when 0  # up
      msgwindow.y=0
    when 1  # middle
      msgwindow.y=(Graphics.height/2)-(msgwindow.height/2)
    when 2
      if $game_temp.speechbubble_bubble==1
       msgwindow.setSkin("Graphics/windowskins/bubbleskinnew")
       msgwindow.height = 100
       msgwindow.width = 400
     elsif $game_temp.speechbubble_bubble==2
       msgwindow.setSkin("Graphics/windowskins/bubbleskinnew")
       msgwindow.height = 102
       msgwindow.width = Graphics.width
       # Position bubble based on event location
       if $game_temp.speechbubble_talking == -1
         speaker_x = $game_player.screen_x
         speaker_y = $game_player.screen_y
       else
         speaker = $game_map.events[$game_temp.speechbubble_talking]
         speaker_x = speaker.screen_x
         speaker_y = speaker.screen_y
       end

       # Center bubble horizontally on the event, but keep it on screen
       msgwindow.x = speaker_x - (msgwindow.width / 2)
       if msgwindow.x < 0
         msgwindow.x = 0
       elsif msgwindow.x + msgwindow.width > Graphics.width
         msgwindow.x = Graphics.width - msgwindow.width
       end

       # Position bubble above or below event based on screen position
       if speaker_y < Graphics.height / 2
         # Event in upper half - bubble below
         msgwindow.y = speaker_y + 64
         $game_temp.speechbubble_outofrange = false
       else
         # Event in lower half - bubble above
         msgwindow.y = speaker_y - msgwindow.height - 64
         $game_temp.speechbubble_outofrange = true
       end

       # Initialize animation properties
       $game_temp.speechbubble_animating = true
       $game_temp.speechbubble_animation_frame = 0
       if speaker_y < Graphics.height / 2
         $game_temp.speechbubble_slide_offset = -30  # Slide down from above
       else
         $game_temp.speechbubble_slide_offset = 30   # Slide up from below
       end
      else
        msgwindow.height = 102
        msgwindow.y = Graphics.height - msgwindow.height - 6
      end
    end
  end
  if $game_system && $game_system.respond_to?("message_frame")
    if $game_system.message_frame != 0
      msgwindow.opacity = 0
    end
  end
  if $game_message
    case $game_message.background
      when 1  # dim
        msgwindow.opacity=0
      when 2  # transparent
        msgwindow.opacity=0
    end
  end
end
 
def pbCreateMessageWindow(viewport = nil, skin = nil)
  arrow = nil
  if $game_temp.speechbubble_bubble == 2 && ( $game_temp.speechbubble_talking == -1 || $game_map.events[$game_temp.speechbubble_talking] != nil)
    # Determine speaker x and y (player or event)
    if $game_temp.speechbubble_talking == -1
      speaker_x = $game_player.screen_x
      speaker_y = $game_player.screen_y
    else
      speaker = $game_map.events[$game_temp.speechbubble_talking]
      speaker_x = speaker.screen_x
      speaker_y = speaker.screen_y
    end

    # Create viewport for the entire screen
    $game_temp.speechbubble_vp = Viewport.new(0, 0, Graphics.width, Graphics.height)
    $game_temp.speechbubble_vp.z = 999999

    # Create arrow sprite
    arrow = Sprite.new($game_temp.speechbubble_vp)
    arrow.z = 999999
    arrow.opacity = 0  # Start invisible for animation

    # Determine if bubble should appear above or below the event
    # If event is in upper half of screen, bubble appears below (arrow points up)
    # If event is in lower half of screen, bubble appears above (arrow points down)
    if speaker_y < Graphics.height / 2
      # Event in upper half - bubble below, arrow points up
      arrow.bitmap = RPG::Cache.load_bitmap("Graphics/Pictures/","Arrow_Up")
      $game_temp.speechbubble_outofrange = false
    else
      # Event in lower half - bubble above, arrow points down
      arrow.bitmap = RPG::Cache.load_bitmap("Graphics/Pictures/","Arrow_Down")
      $game_temp.speechbubble_outofrange = true
    end

    # Position arrow directly above/below the event
    arrow.x = speaker_x - (arrow.bitmap.width / 2)
    if speaker_y < Graphics.height / 2
      arrow.y = speaker_y + 32  # Below the event
    else
      arrow.y = speaker_y - 32 - arrow.bitmap.height  # Above the event
    end
  end
  $game_temp.speechbubble_arrow = arrow
  msgwindow=Window_AdvancedTextPokemon.new("")
  if !viewport
    msgwindow.z=99999
  else
    msgwindow.viewport=viewport
  end
  msgwindow.visible=true
  msgwindow.letterbyletter=true

  # Set initial opacity based on whether we're animating
  if $game_temp.speechbubble_bubble && $game_temp.speechbubble_bubble > 0
    msgwindow.opacity = 0
    msgwindow.back_opacity = 0
  else
    msgwindow.back_opacity = MessageConfig::WINDOW_OPACITY
  end

  pbBottomLeftLines(msgwindow,2)
  $game_temp.message_window_showing=true if $game_temp
  $game_message.visible=true if $game_message
  skin=MessageConfig.pbGetSpeechFrame() if !skin
  msgwindow.setSkin(skin)
  return msgwindow
end

def pbDisposeMessageWindow(msgwindow)
  $game_temp.message_window_showing=false if $game_temp
  $game_message.visible=false if $game_message
  msgwindow.dispose
  $game_temp.speechbubble_arrow.dispose if $game_temp.speechbubble_arrow
  $game_temp.speechbubble_vp.dispose if $game_temp.speechbubble_vp
end

# Animation update function
def pbUpdateSpeechBubbleAnimation(msgwindow)
  return unless $game_temp.speechbubble_animating

  $game_temp.speechbubble_animation_frame += 1
  animation_duration = 20  # frames for animation

  if $game_temp.speechbubble_animation_frame <= animation_duration
    # Calculate animation progress (0.0 to 1.0)
    progress = $game_temp.speechbubble_animation_frame.to_f / animation_duration

    # Ease-out animation curve
    progress = 1 - (1 - progress) ** 3

    # Fade in the bubble
    msgwindow.opacity = (255 * progress).to_i
    msgwindow.back_opacity = (MessageConfig::WINDOW_OPACITY * progress).to_i

    # Slide animation for speech bubbles with arrows
    if $game_temp.speechbubble_bubble == 2
      slide_amount = $game_temp.speechbubble_slide_offset * (1 - progress)
      # Store the original Y position if not already stored
      if !$game_temp.speechbubble_original_y
        $game_temp.speechbubble_original_y = msgwindow.y
      end
      msgwindow.y = $game_temp.speechbubble_original_y + slide_amount.to_i
    end
  else
    # Animation complete
    $game_temp.speechbubble_animating = false
    msgwindow.opacity = 255
    msgwindow.back_opacity = MessageConfig::WINDOW_OPACITY

    # Reset to original position
    if $game_temp.speechbubble_original_y
      msgwindow.y = $game_temp.speechbubble_original_y
      $game_temp.speechbubble_original_y = nil
    end

    # Show arrow after animation completes
    if $game_temp.speechbubble_arrow
      $game_temp.speechbubble_arrow.opacity = 255
    end
  end
end

def pbCallBub(type, value)
  if value == -1
    $game_temp.speechbubble_talking = -1
  else
    $game_temp.speechbubble_talking = get_character(value).id
  end
  $game_temp.speechbubble_bubble = type
  # Reset animation state
  $game_temp.speechbubble_animating = false
  $game_temp.speechbubble_animation_frame = 0
  $game_temp.speechbubble_slide_offset = 0
end