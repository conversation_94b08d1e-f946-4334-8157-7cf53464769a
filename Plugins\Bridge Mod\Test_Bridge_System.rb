#===============================================================================
# Bridge System Test Script
# Use this to test and debug the bridge NPC system
#===============================================================================

def test_bridge_system
  if !$game_map
    pbMessage("No game map loaded!")
    return
  end
  
  bridge_state = $PokemonGlobal.bridge > 0 ? "ON" : "OFF"
  result = "Bridge System Test\n"
  result += "==================\n"
  result += "Bridge State: #{bridge_state}\n"
  result += "Player Z: #{$game_player.screen_z}\n\n"
  
  # Find all bridge NPCs
  bridge_npcs = []
  $game_map.events.each_value do |event|
    next if !event || !event.name || event.name == ""
    if event.name[/bridge_(above|below)/i]
      bridge_npcs.push(event)
    end
  end
  
  if bridge_npcs.empty?
    result += "No bridge NPCs found on this map.\n"
    result += "Make sure NPCs have 'bridge_above' or 'bridge_below' in their names."
  else
    result += "Bridge NPCs found:\n"
    result += "-" * 40 + "\n"
    
    bridge_npcs.each do |event|
      position = event.name[/bridge_above/i] ? "ABOVE" : "BELOW"
      z_value = event.screen_z
      through_state = event.through ? "PASSABLE" : "SOLID"
      
      result += "#{event.name} (ID: #{event.id})\n"
      result += "  Position: #{position}\n"
      result += "  Z-Value: #{z_value}\n"
      result += "  State: #{through_state}\n"
      result += "  Expected behavior:\n"
      
      if position == "ABOVE"
        if bridge_state == "ON"
          result += "    - Should be at normal level, passable\n"
        else
          result += "    - Should be above bridge area, solid\n"
        end
      else # BELOW
        if bridge_state == "ON"
          result += "    - Should be below bridge, passable\n"
        else
          result += "    - Should be at normal level, solid\n"
        end
      end
      result += "\n"
    end
  end
  
  pbMessage(result)
end

# Quick toggle function for testing
def toggle_bridge_for_test
  if $PokemonGlobal.bridge > 0
    pbBridgeOff
    pbMessage("Bridge turned OFF")
  else
    pbBridgeOn(2)
    pbMessage("Bridge turned ON")
  end
  
  # Run test after toggle
  test_bridge_system
end

# Function to check z-ordering issues
def check_z_ordering
  if !$game_map
    pbMessage("No game map loaded!")
    return
  end
  
  result = "Z-Ordering Analysis\n"
  result += "===================\n"
  result += "Player Z: #{$game_player.screen_z}\n\n"
  
  # Get all events and sort by z-value
  all_events = []
  $game_map.events.each_value do |event|
    next if !event || !event.name || event.name == ""
    all_events.push({
      name: event.name,
      id: event.id,
      z: event.screen_z,
      through: event.through
    })
  end
  
  all_events.sort! { |a, b| b[:z] <=> a[:z] }  # Sort by z-value, highest first
  
  result += "Events by Z-order (highest to lowest):\n"
  result += "-" * 40 + "\n"
  
  all_events.each do |event_data|
    through_text = event_data[:through] ? " (PASSABLE)" : " (SOLID)"
    result += "Z #{event_data[:z]}: #{event_data[:name]}#{through_text}\n"
  end
  
  pbMessage(result)
end
