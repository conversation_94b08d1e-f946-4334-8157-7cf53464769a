#===============================================================================
# Enhanced Bridge System for Pokemon Essentials v21.1
# By GitHub Copilot, based on user requirements
#===============================================================================
#
# USAGE:
# 1. Include "bridge_above" in an event's name for NPCs that should appear
#    above the bridge area when bridges are OFF (like guards on a bridge)
#
# 2. Include "bridge_below" in an event's name for NPCs that should appear
#    below bridges when bridges are ON (like swimmers under a bridge)
#
# The system automatically handles z-ordering and interactability based on
# the bridge state and the NPC's designated position.
#===============================================================================

# This class handles NPCs that have different behaviors/visibility based on bridge state
class BridgeLayeredNPC
  attr_reader :event_id
  attr_reader :map_id
  attr_reader :position
  attr_accessor :above_sprite
  attr_accessor :below_sprite
  
  # Position can be :above or :below
  def initialize(event_id, map_id, position)
    @event_id = event_id
    @map_id = map_id
    @position = position # :above or :below
    @above_sprite = nil
    @below_sprite = nil
    @initialized = false
    @event = nil
  end
  
  # Get the event this NPC is attached to
  def event
    # Lazy load the event
    if !@event || @event.disposed?
      @event = $map_factory.getMap(@map_id).events[@event_id]
    end
    return @event
  end
  
  # Update this NPC's visibility and interactability based on bridge state
  def update
    return if !$game_map || !event
    bridge_on = $PokemonGlobal.bridge > 0
    
    # Skip update if the event has bridge_above or bridge_below in its name
    # as those will be handled by the automatic system
    return if event.name[/bridge_(above|below)/i]
    
    if @position == :above
      # Above bridge behavior: through when bridge is on (player walks above them), solid when bridge is off
      event.through = bridge_on
    else
      # Below bridge behavior: through when bridge is on (they're below), solid when bridge is off
      event.through = bridge_on
    end
  end
end

# Module to manage the layered NPCs
module BridgeLayeredNPCSystem
  @layered_npcs = []
  
  class << self
    attr_reader :layered_npcs
    
    # Register a new layered NPC
    def register(event_id, map_id, position)
      # Check if this NPC already exists
      existing = @layered_npcs.find { |npc| npc.event_id == event_id && npc.map_id == map_id }
      if existing
        # If it exists, just update its position
        existing.position = position
        return existing
      end
      
      # Create new NPC object
      npc = BridgeLayeredNPC.new(event_id, map_id, position)
      @layered_npcs.push(npc)
      return npc
    end
    
    # Register an NPC that appears above bridges
    def register_above_bridge(event_id, map_id = $game_map.map_id)
      return register(event_id, map_id, :above)
    end
    
    # Register an NPC that appears below bridges
    def register_below_bridge(event_id, map_id = $game_map.map_id)
      return register(event_id, map_id, :below)
    end
    
    # Update all layered NPCs
    def update_all
      @layered_npcs.each { |npc| npc.update }
    end
    
    # Clear all registered NPCs
    def clear
      @layered_npcs.clear
    end
    
    # Find all registered NPCs for a specific map
    def for_map(map_id)
      return @layered_npcs.select { |npc| npc.map_id == map_id }
    end
  end
end

# Extend the Game_Event class for z-ordering with bridges
class Game_Event
  alias original_screen_z_for_bridge screen_z
  
  # Check if this event should be treated as a layered NPC based on name
  def bridge_layer_position
    return nil if !@event || !@event.name
    
    if @event.name[/bridge_above/i]
      return :above
    elsif @event.name[/bridge_below/i]
      return :below
    else
      # Check registered NPCs (for backward compatibility with script method)
      layered_npc = BridgeLayeredNPCSystem.layered_npcs.find { |npc| npc.event_id == @id && npc.map_id == @map_id }
      return layered_npc ? layered_npc.position : nil
    end
  end
  
  def screen_z(height = 0)
    # Get position based on name or registration
    position = bridge_layer_position
    bridge_on = $PokemonGlobal.bridge > 0

    if position
      if position == :above
        if bridge_on
          # When bridge is on, above NPCs should be at normal level (same as player)
          return original_screen_z_for_bridge(height)
        else
          # When bridge is off, they should appear above the bridge area
          # Use a high z-value to ensure they appear above bridge tiles
          return original_screen_z_for_bridge(height) + 100
        end
      elsif position == :below
        if bridge_on
          # When bridge is on, below NPCs should be below the bridge
          return original_screen_z_for_bridge(height) - 50
        else
          # When bridge is off, they use normal z-ordering
          return original_screen_z_for_bridge(height)
        end
      end
    end

    # Default behavior if not a special case
    return original_screen_z_for_bridge(height)
  end
  
  # Override the passable check to handle bridge layering
  alias original_passable_for_bridge passable?
  
  def passable?(x, y, d, strict = false)
    # Get position based on name or registration
    position = bridge_layer_position
    bridge_on = $PokemonGlobal.bridge > 0

    if position
      if position == :above
        # Above bridge NPCs: solid when bridge is off (they're above), passable when bridge is on (player is above them)
        return bridge_on
      else
        # Below bridge NPCs: passable when bridge is on (they're below), solid when bridge is off (normal interaction)
        return bridge_on
      end
    end

    # Use original behavior for non-layered NPCs
    return original_passable_for_bridge(x, y, d, strict)
  end
end

# Enhanced bridge functions with NPC updates
alias original_pbBridgeOn pbBridgeOn
def pbBridgeOn(height = 2)
  original_pbBridgeOn(height)
  BridgeLayeredNPCSystem.update_all
end

alias original_pbBridgeOff pbBridgeOff
def pbBridgeOff
  original_pbBridgeOff
  BridgeLayeredNPCSystem.update_all
end

# Helper method to create a layered NPC setup with both above and below instances
def pbCreateLayeredNPC(event_above_id, event_below_id, map_id = $game_map.map_id)
  BridgeLayeredNPCSystem.register_above_bridge(event_above_id, map_id)
  BridgeLayeredNPCSystem.register_below_bridge(event_below_id, map_id)
end

# Update layered NPCs when the map changes
EventHandlers.add(:on_map_change, :bridge_system_update,
  proc {
    BridgeLayeredNPCSystem.update_all
  }
)

# Hook into the frame update to handle bridge events
class Game_Map
  alias bridge_system_update update
  def update
    bridge_system_update
    
    # Handle bridge NPCs on every update
    if self && $PokemonGlobal
      bridge_on = $PokemonGlobal.bridge > 0
      # Update all events on the current map that have bridge_ in their name
      self.events.each_value do |event|
        next if !event || !event.name
        if event.name[/bridge_above/i]
          # Above bridge NPCs: through when bridge is on (player walks above them), solid when bridge is off (normal interaction)
          event.through = bridge_on
        elsif event.name[/bridge_below/i]
          # Below bridge NPCs: through when bridge is on (they're below), solid when bridge is off (normal interaction)
          event.through = bridge_on
        end
      end
      
      # Also update any manually registered NPCs (for backward compatibility)
      BridgeLayeredNPCSystem.update_all if defined?(BridgeLayeredNPCSystem)
    end
  end
end

# These methods are maintained for backward compatibility
def pbLayeredNPCAbove(event_id = nil)
  # Get the event ID from the interpreter if not specified
  if !event_id && $game_map && $game_system && $game_system.map_interpreter
    event_id = $game_system.map_interpreter.event_id
  end
  BridgeLayeredNPCSystem.register_above_bridge(event_id) if event_id
end

def pbLayeredNPCBelow(event_id = nil)
  # Get the event ID from the interpreter if not specified
  if !event_id && $game_map && $game_system && $game_system.map_interpreter
    event_id = $game_system.map_interpreter.event_id
  end
  BridgeLayeredNPCSystem.register_below_bridge(event_id) if event_id
end

# Debug function to check NPC z-ordering
def pbDebugBridgeNPCs
  if $game_map
    result = "Bridge state: " + ($PokemonGlobal.bridge > 0 ? "ON" : "OFF") + "\n"
    result += "Events with z-values:\n"
    $game_map.events.each_value do |event|
      next if !event || !event.name || event.name == ""
      position = nil
      if event.name[/bridge_above/i]
        position = "above"
      elsif event.name[/bridge_below/i]
        position = "below"
      end
      z_value = event.screen_z
      through = event.through ? "passable" : "solid"
      result += "#{event.id}: #{event.name} - z: #{z_value}, #{through}"
      result += " (#{position})" if position
      result += "\n"
    end
    
    pbMessage(result)
  end
end

#===============================================================================
# Instructions:
#===============================================================================
# For events to automatically work with bridges:
# 
# 1. For NPCs that should appear ABOVE bridges when bridge is ON:
#    - Include "bridge_above" in the event's name
#    - Example: "Guard bridge_above"
#
# 2. For NPCs that should appear BELOW bridges when bridge is OFF:
#    - Include "bridge_below" in the event's name
#    - Example: "Swimmer bridge_below"
#
# When the bridge is ON:
# - "bridge_above" NPCs will be at normal level, passable (player walks above them)
# - "bridge_below" NPCs will be below the bridge level, passable (they're underneath)
#
# When the bridge is OFF:
# - "bridge_above" NPCs will appear above the bridge area with high z-value, solid and interactable
# - "bridge_below" NPCs will be at normal level, solid and interactable
#===============================================================================
